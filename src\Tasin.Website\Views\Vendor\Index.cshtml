﻿@{
    ViewData["Title"] = "Nhà cung cấp";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <style>
        /* Product Vendor Grid Popup Styles */
        .product-vendor-popup {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .search-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e3e6f0;
        }

        .search-container .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .search-input-group {
            position: relative;
        }

        .search-input-group .form-control {
            border: 2px solid #e3e6f0;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .search-input-group .form-control:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
            background: white;
        }

        .search-input-group .btn {
            border-radius: 0 8px 8px 0;
            border: 2px solid #e3e6f0;
            border-left: none;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .search-input-group .btn:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .search-help-text {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
            font-style: italic;
        }

        .search-result-count {
            background: linear-gradient(45deg, #4e73df, #36b9cc);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            box-shadow: 0 2px 10px rgba(78, 115, 223, 0.3);
        }

        /* Grid Container Styles */
        .grid-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e3e6f0;
        }

        /* Kendo Grid Customization */
        .k-grid .k-grid-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        .k-grid .k-grid-header .k-header {
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
            text-align: center;
            padding: 12px 8px;
        }

        .k-grid .k-alt {
            background-color: #f8f9fa;
        }

        .k-grid tbody tr {
            transition: all 0.2s ease;
        }

        .k-grid tbody tr:hover {
            background-color: #e3f2fd !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .k-grid .k-grid-content td {
            padding: 12px 8px;
            border-color: #e9ecef;
            vertical-align: middle;
        }

        /* Toolbar Styles */
        .k-grid .k-grid-toolbar {
            background: linear-gradient(135deg, #36b9cc 0%, #28a745 100%);
            border: none;
            padding: 15px;
        }

        .k-grid .k-grid-toolbar .k-button {
            background: white;
            color: #495057;
            border: 2px solid white;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 600;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .k-grid .k-grid-toolbar .k-button:hover {
            background: #495057;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* ComboBox Dropdown Styles */
        .k-combobox .k-dropdown-wrap {
            border: 2px solid #e3e6f0;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .k-combobox .k-dropdown-wrap:hover {
            border-color: #4e73df;
        }

        .k-combobox .k-input {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* Product Template in Dropdown */
        .product-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.2s ease;
        }

        .product-item:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        }

        .product-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .product-code {
            color: #6c757d;
            font-size: 12px;
            font-style: italic;
        }

        /* Action Buttons */
        .k-grid .k-command-cell .k-button {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .k-grid .k-command-cell .k-button:hover {
            background: #c82333;
            transform: scale(1.05);
        }

        /* Numeric TextBox Styles */
        .k-numerictextbox .k-textbox {
            border: 2px solid #e3e6f0;
            border-radius: 4px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        .k-numerictextbox .k-textbox:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
        }

        /* Window Styles */
        .k-window {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .k-window .k-window-titlebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
        }

        .k-window .k-window-content {
            padding: 0;
        }

        /* Loading Animation */
        .search-loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4e73df;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Styles */
        @@media (max-width: 768px) {
            .search-container {
                padding: 15px;
                margin-bottom: 15px;
            }

            .search-input-group .form-control {
                font-size: 16px; /* Prevent zoom on iOS */
            }

            .k-grid .k-grid-header .k-header {
                padding: 8px 4px;
                font-size: 12px;
            }

            .k-grid .k-grid-content td {
                padding: 8px 4px;
                font-size: 12px;
            }

            .search-result-count {
                font-size: 11px;
                padding: 6px 12px;
            }
        }

        /* Animation for new rows */
        @@keyframes slideInFromTop {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .k-grid tbody tr.new-row {
            animation: slideInFromTop 0.5s ease-out;
        }

        /* Focus styles for accessibility */
        .search-input-group .form-control:focus,
        .k-combobox .k-input:focus,
        .k-numerictextbox .k-textbox:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.2);
        }

        /* Custom scrollbar for grid */
        .k-grid .k-grid-content::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .k-grid .k-grid-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .k-grid .k-grid-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .k-grid .k-grid-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";
    let ListProduct_ID = [];


    function addVendorProduct(vendorId) {
        let myWindow = $("#window");
        $("#window").html(`
            <div class="product-vendor-popup">
                <div class="search-container">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="productSearchInput" class="form-label">
                                <i class="fas fa-search"></i> Tìm kiếm trong danh sách
                            </label>
                            <div class="input-group search-input-group">
                                <input type="text" id="productSearchInput" class="form-control"
                                       placeholder="Tìm kiếm theo tên sản phẩm, mô tả..." />
                                <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                    <i class="fas fa-times"></i> Xóa
                                </button>
                            </div>
                            <div class="search-help-text">
                                <i class="fas fa-info-circle"></i> Tìm kiếm theo tên sản phẩm hoặc mô tả
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end justify-content-end">
                            <div class="search-result-count" id="searchResultCount">
                                <i class="fas fa-list"></i> Đang tải...
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-container">
                    <div id='productVendorGrid'></div>
                </div>
            </div>
        `);

        let title = "THÊM NHIỀU HÀNG HOÁ CHO NHÀ CUNG CẤP";

        // Function to update search result count
        function updateSearchResultCount(count) {
            var grid = $("#productVendorGrid").data("kendoGrid");
            if (!grid) return;

            var totalCount = grid.dataSource.data().length;
            var searchValue = $("#productSearchInput").val().trim();
            var countElement = $("#searchResultCount");

            if (searchValue === "") {
                countElement.html(`<i class="fas fa-list"></i> Tổng: ${totalCount} sản phẩm`);
                countElement.removeClass('search-loading');
            } else {
                countElement.html(`<i class="fas fa-filter"></i> Hiển thị: ${count}/${totalCount} sản phẩm`);
                countElement.removeClass('search-loading');
            }

            // Add a subtle animation
            countElement.addClass('animate__animated animate__pulse');
            setTimeout(() => {
                countElement.removeClass('animate__animated animate__pulse');
            }, 600);
        }

        // Function to initialize the grid with data
        function initializeGrid(productVendorData) {
            // Initialize grid for adding multiple products
            $("#productVendorGrid").kendoGrid({
                dataSource: {
                    data: productVendorData,
                    schema: {
                        model: {
                            id: "Product_ID",
                            fields: {
                                Product_ID: { type: "number", validation: { required: true } },
                                ProductName: { type: "string", editable: false },
                                UnitPrice: { type: "number" },
                                Priority: { type: "number" },
                                Description: { type: "string" }
                            }
                        },
                        parse: function (response) {
                            ListProduct_ID = response.map(e => e.Product_ID);
                            return response;
                        },
                    }
                },
                height: 450,
                scrollable: true,
                sortable: true,
                filterable: {
                    mode: "row"
                },
                editable: {
                    mode: "incell",
                    createAt: "top"
                },
                toolbar: [
                    { name: "create", text: "Thêm sản phẩm" },
                    { name: "save", text: "Lưu tất cả" },
                    { name: "cancel", text: "Hủy" }
                ],
                columns: [
                    {
                        field: "Product_ID",
                        title: "Sản phẩm (*)",
                        width: 200,
                        editor: function (container, options) {
                            $('<input required data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoComboBox({
                                    dataTextField: "name",
                                    dataValueField: "id",
                                    placeholder: "Tìm kiếm sản phẩm...",
                                    clearButton: false,
                                    suggest: true,
                                    filter: "contains",
                                    minLength: 1,
                                    highlightFirst: true,
                                    dataSource: {
                                        transport: {
                                            read: {
                                                url: "/Product/GetProductList",
                                                datatype: "json",
                                            },
                                            parameterMap: function (data, type) {
                                                if (type == "read") {
                                                    return {
                                                        pageSize: 100,
                                                        pageNumber: 1,
                                                        searchString: data.filter && data.filter.filters && data.filter.filters.length > 0
                                                            ? data.filter.filters[0].value : ""
                                                    }
                                                }
                                            },
                                        },
                                        serverPaging: true,
                                        serverFiltering: true,
                                        pageSize: 100,
                                        schema: {
                                            type: 'json',
                                            parse: function (response) {
                                                if (response.isSuccess == false) {
                                                    showErrorMessages(response.errorMessageList);
                                                    return { data: [], total: 0 }
                                                }
                                                // return response.data.data.filter(e=> !ListProduct_ID.includes(e.id));
                                                return response.data;
                                            },
                                            model: {
                                                id: "id",
                                                fields: {
                                                    name: { type: "string" },
                                                    code: { type: "string" }
                                                }
                                            },
                                            data: "data",
                                            total: "total"
                                        },
                                    },
                                    template: function (dataItem) {
                                        if (!dataItem || !dataItem.name) {
                                            return "N/A";
                                        }
                                        return "<div class='product-item'>" +
                                               "<div class='product-name'>" + dataItem.name + "</div>" +
                                               "<div class='product-code'>Mã: " + (dataItem.code || "N/A") + "</div>" +
                                               "</div>";
                                    },
                                    valueTemplate: function (dataItem) {
                                        if (!dataItem || !dataItem.name) {
                                            return "";
                                        }
                                        return dataItem.name;
                                    },
                                    change: function (e) {
                                        var selectedItem = this.dataItem();
                                        if (selectedItem) {
                                            var grid = $("#productVendorGrid").data("kendoGrid");
                                            var dataItem = grid.dataItem(container.closest("tr"));
                                            dataItem.set("ProductName", selectedItem.name);
                                            dataItem.ProductName = selectedItem.name;
                                            ListProduct_ID.push(selectedItem.id);
                                        }
                                    }
                                });
                        },
                        // template: "#= ProductName || '' #"
                        template: function (dataItem) {

                            return dataItem.ProductName || "";
                        }
                    },
                    {
                        field: "UnitPrice",
                        title: "Đơn giá",
                        width: 120,
                        format: "{0:n0}",
                        editor: function (container, options) {
                            $('<input data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n0",
                                    decimals: 0
                                });
                        }
                    },
                    {
                        field: "Priority",
                        title: "Ưu tiên",
                        width: 100,
                        editor: function (container, options) {
                            $('<input data-bind="value:' + options.field + '"/>')
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n0",
                                    decimals: 0,
                                    min: 1
                                });
                        }
                    },
                    {
                        field: "Description",
                        title: "Mô tả",
                        width: 200
                    },
                    {
                        command: ["destroy"],
                        title: "Thao tác",
                        width: 100
                    }
                ],
                save: function (e) {
                    // Handle save event for individual rows
                },
                dataBound: function (e) {
                    // Update search result count when grid data is bound
                    var grid = this;
                    var filteredCount = grid.dataSource.view().length;
                    updateSearchResultCount(filteredCount);
                }
            });

            // Handle toolbar save button
            $("#productVendorGrid").on("click", ".k-grid-save-changes", function (e) {
                e.preventDefault();
                saveAllProductVendors(vendorId);
            });

            // Handle search functionality with debounce for better performance
            let searchTimeout;
            $("#productSearchInput").on("input", function () {
                clearTimeout(searchTimeout);

                // Show loading state
                $("#searchResultCount").html('<i class="fas fa-spinner fa-spin"></i> Đang tìm kiếm...');
                $("#searchResultCount").addClass('search-loading');

                searchTimeout = setTimeout(() => {
                var searchValue = $(this).val().trim();
                var grid = $("#productVendorGrid").data("kendoGrid");

                if (searchValue === "") {
                    grid.dataSource.filter({});
                    updateSearchResultCount(grid.dataSource.total());
                } else {
                    // Search in multiple fields: ProductName and Description (case-insensitive)
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            {
                                field: "ProductName",
                                operator: "contains",
                                value: searchValue,
                                ignoreCase: true
                            },
                            {
                                field: "Description",
                                operator: "contains",
                                value: searchValue,
                                ignoreCase: true
                            }
                        ]
                    });
                }
                }, 300); // 300ms debounce
            });

            // Handle Enter key for search
            $("#productSearchInput").on("keypress", function (e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    $(this).trigger("input");
                }
            });

            // Handle clear search button
            $("#clearSearchBtn").click(function () {
                $("#productSearchInput").val("");
                var grid = $("#productVendorGrid").data("kendoGrid");
                grid.dataSource.filter({});
                updateSearchResultCount(grid.dataSource.total());
            });



            function remove() {
                setTimeout(() => {
                    if ($(".k-window #window").length > 0) {
                        $("#window").parent().remove();
                        $(gridId).after("<div id='window'></div>");
                    }
                }, 200)
            }

            myWindow.kendoWindow({
                width: "1000px",
                height: "700px",
                title: "",
                visible: false,
                actions: ["Close"],
                resizable: true,
                draggable: true,
                modal: true,
                close: function (e) {
                    remove();
                },
            }).data("kendoWindow").title(title).center();
            myWindow.data("kendoWindow").open();
        }

        // Load existing products for this vendor
        ajax("GET", "/ProductVendor/GetProductsByVendorId/" + vendorId, null, (response) => {
            let productVendorData = [];
            if (response.isSuccess && response.data) {
                // Convert API response to grid format
                productVendorData = response.data.map(item => ({
                    Product_ID: item.product_ID,
                    ProductName: item.productName,
                    UnitPrice: item.unitPrice,
                    Priority: item.priority,
                    Description: item.description
                }));
            }
            initializeGrid(productVendorData);
        }, (error) => {
            // Error callback - initialize grid with empty data if API call fails
            initializeGrid([]);
        }, false); // End of ajax call
    }

    function saveAllProductVendors(vendorId) {
        var grid = $("#productVendorGrid").data("kendoGrid");
        var data = grid.dataSource.data();

        if (data.length === 0) {
            showErrorMessages(["Vui lòng thêm ít nhất một sản phẩm."]);
            return;
        }

        // Validate required fields
        var hasErrors = false;
        var products = [];

        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (!item.Product_ID) {
                showErrorMessages(["Vui lòng chọn sản phẩm cho tất cả các dòng."]);
                hasErrors = true;
                break;
            }

            products.push({
                Product_ID: item.Product_ID,
                UnitPrice: item.UnitPrice || 0,
                Priority: item.Priority || 1,
                Description: item.Description || ""
            });
        }

        if (hasErrors) return;

        var requestData = {
            VendorId: vendorId,
            Products: products
        };

        var response = ajax("POST", "/ProductVendor/BulkAddProductsToVendor", requestData, () => {
            $(gridId).data("kendoGrid").dataSource.filter({});
            $("#window").data("kendoWindow").close();
            showSuccessMessages(["Thêm sản phẩm thành công!"]);
        }, null, false);
    }

    function renderCreateOrEditForm(isCreate = true, dataVendor = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            name: "",
            address: "",
            // status: "",
            ...dataVendor
        };
        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [

                {
                    field: "name",
                    title: "Họ tên",
                    label: "Họ tên (*):",
                    validation: {
                        validationMessage: "Vui lòng nhập họ tên",
                        required: true
                    },
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    label: "Địa chỉ:",
                    validation: {
                        address: true
                    },
                },

            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };

                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/Vendor/UpdateVendor/" + dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else {
                    var response = ajax("POST", "/Vendor/Create", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if (!isCreate) {

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editVendor(id) {
        var response = ajax("GET", "/Vendor/GetVendorById/" + id, {}, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteVendor(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA NHÀ CUNG CẤP",
            content: "Bạn có chắc chắn xóa nhà cung cấp này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Vendor/DeleteVendorById/" + id, {
                vendorId: id
            }, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }


    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    {
                        value: "Mã nhà cung cấp", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Họ tên", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Địa chỉ", textAlign: "center", background: "#428dd8"
                    },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceVendor = null;
        var response = await ajax("GET", "/Vendor/GetVendorList", postData, (urnResponse) => {
            dataSourceVendor = urnResponse.data.data;
        }, null, false);
        if (dataSourceVendor == null) return;

        for (let index = 0; index < dataSourceVendor.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceVendor[index].code },
                    { value: dataSourceVendor[index].name },
                    { value: dataSourceVendor[index].address },
                ]
            })
        }


        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách nhà cung cấp",
                    columns: [
                        { width: 200 }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách nhà cung cấp _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='16'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Vendor/GetVendorList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: 100
                },
                {
                    field: "code",
                    title: "Mã nhà cung cấp",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "name",
                    title: "Họ tên",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate || createdDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: 200, attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        return '<div class="action-buttons">' +
                            '<button onclick="addVendorProduct(' + dataItem.id + ')" title="Thêm hàng hoá" class="btn-action btn-add _permission_" data-enum="8">' +
                            '<i class="fas fa-plus"></i>' +
                            '</button>' +
                            '<button onclick="editVendor(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="8">' +
                            '<i class="fas fa-edit"></i>' +
                            '</button>' +
                            '<button onclick="deleteVendor(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="9">' +
                            '<i class="fas fa-trash"></i>' +
                            '</button>' +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

    };

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Add product button styling */
    .btn-add {
        background: #28a745;
        color: white;
    }

    .btn-add:hover {
        background: #218838;
    }
</style>
