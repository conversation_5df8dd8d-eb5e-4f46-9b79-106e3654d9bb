﻿@{
    ViewData["Title"] = "Danh sách đơn hàng PO";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" />
    <link href="~/css/form-common.css" rel="stylesheet" />
}

<div>
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";

    function getSearchModel() {
        let searchString = $("#searchString").val();
        let customerId = $("#customerId").data("kendoDropDownList")?.value();
        let status = $("#status").data("kendoDropDownList")?.value();
        let dateFromPicker = $("#dateFrom").data("kendoDatePicker");
        let dateToPicker = $("#dateTo").data("kendoDatePicker");

        // Get date values from Kendo DatePickers
        let dateFrom = null;
        let dateTo = null;

        if (dateFromPicker && dateFromPicker.value()) {
            dateFrom = dateFromPicker.value().toISOString();
        }

        if (dateToPicker && dateToPicker.value()) {
            let dateToObj = dateToPicker.value();
            // Set to end of day
            dateToObj.setHours(23, 59, 59, 999);
            dateTo = dateToObj.toISOString();
        }

        return {
            searchString: searchString || null,
            customer_ID: customerId && customerId !== "" ? parseInt(customerId) : null,
            status: status || null,
            dateFrom: dateFrom,
            dateTo: dateTo
        };
    }

    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    { value: "Mã đơn hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Khách hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Trạng thái", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng tiền chưa thuế", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng tiền", textAlign: "center", background: "#428dd8" },
                    { value: "Ngày tạo", textAlign: "center", background: "#428dd8" },
                    { value: "Người tạo", textAlign: "center", background: "#428dd8" }
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourcePO = null;
        var response = await ajax("GET", "/PurchaseOrder/GetPurchaseOrderList", postData, (poResponse) => {
            dataSourcePO = poResponse.data.data;
        }, null, false);
        if (dataSourcePO == null) return;

        for (let index = 0; index < dataSourcePO.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourcePO[index].code },
                    { value: dataSourcePO[index].customerName },
                    { value: dataSourcePO[index].statusName },
                    { value: dataSourcePO[index].totalPriceNoTax },
                    { value: dataSourcePO[index].totalPrice },
                    { value: dataSourcePO[index].createdDate ? kendo.toString(kendo.parseDate(dataSourcePO[index].createdDate), "dd/MM/yyyy HH:mm") : '' },
                    { value: dataSourcePO[index].createdByName }
                ]
            })
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách đơn hàng PO",
                    columns: [
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách đơn hàng PO _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function renderCreateOrEditForm(isCreate = true, dataPO = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            code: "",
            customer_ID: null,
            status: 0,
            totalPrice: 0,
            totalPriceNoTax: 0,
            purchaseOrderItems: [],
            ...dataPO
        };

        let strSubmit = "Thêm";
        let title = "THÊM MỚI ĐƠN HÀNG PO"
        if (isCreate == false) {
            strSubmit = "Cập nhật";
            title = "CẬP NHẬT ĐƠN HÀNG PO";
        }

        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [
                {
                    field: "code",
                    title: "Mã đơn hàng",
                    label: "Mã đơn hàng:",
                    attributes: { readonly: !isCreate }
                },
                {
                    field: "customer_ID",
                    title: "Khách hàng",
                    label: "Khách hàng (*):",
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn khách hàng",
                        dataTextField: "text",
                        dataValueField: "value",
                        filter: "contains",
                        template: function (dataItem) {
                            if (!dataItem || !dataItem.text) {
                                return "N/A";
                            }
                            return dataItem.text;
                        },
                        valueTemplate: function (dataItem) {
                            if (!dataItem || !dataItem.text) {
                                return "Chọn khách hàng";
                            }
                            return dataItem.text;
                        },
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Common/GetDataOptionsDropdown",
                                    data: { type: "Customer" }
                                }
                            },
                            schema: {
                                parse: function (response) {
                                    if (response && response.isSuccess && response.data) {
                                        return response.data.filter(function (item) {
                                            return item && item.text && item.value !== undefined;
                                        });
                                    }
                                    return [];
                                }
                            }
                        },
                    },
                    validation: {
                        validationMessage: "Vui lòng chọn khách hàng",
                        required: true
                    },
                },
                {
                    field: "status",
                    title: "Trạng thái",
                    label: "Trạng thái:",
                    editor: "DropDownList",
                    editorOptions: {
                        dataTextField: "text",
                        dataValueField: "dataRaw",
                        template: function (dataItem) {
                            if (!dataItem || !dataItem.text) {
                                return "N/A";
                            }
                            return dataItem.text;
                        },
                        valueTemplate: function (dataItem) {
                            if (!dataItem || !dataItem.text) {
                                return "Chọn trạng thái";
                            }
                            return dataItem.text;
                        },
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Common/GetDataOptionsDropdown",
                                    data: { type: "POStatus" }
                                }
                            },
                            schema: {
                                parse: function (response) {
                                    if (response && response.isSuccess && response.data) {
                                        return response.data.filter(function (item) {
                                            return item && item.text && item.dataRaw !== undefined;
                                        });
                                    }
                                    return [];
                                }
                            }
                        }
                    }
                },
                {
                    field: "totalPriceNoTax",
                    title: "Tổng tiền chưa thuế",
                    label: "Tổng tiền chưa thuế:",
                    editor: "NumericTextBox",
                    editorOptions: {
                        format: "n0",
                        decimals: 0
                    },
                    attributes: { readonly: true }
                },
                {
                    field: "totalPrice",
                    title: "Tổng tiền",
                    label: "Tổng tiền:",
                    editor: "NumericTextBox",
                    editorOptions: {
                        format: "n0",
                        decimals: 0
                    },
                    attributes: { readonly: true }
                }
            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };

                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/PurchaseOrder/UpdatePurchaseOrder", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                } else {
                    var response = ajax("POST", "/PurchaseOrder/CreatePurchaseOrder", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });

        setTimeout(() => {
            $("input[title='code']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "600px",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editPurchaseOrder(id) {
        var response = ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, (response) => {
            renderPurchaseOrderForm(response.data);
        }, null, false);
    }

    function deletePurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn xóa đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/PurchaseOrder/DeletePurchaseOrderById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function cancelPurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO HỦY ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn hủy đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("PUT", "/PurchaseOrder/CancelPurchaseOrderById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function confirmPurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn muốn xác nhận đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            // Get current purchase order data first
            ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, function (response) {
                if (response.isSuccess && response.data) {
                    var purchaseOrder = response.data;
                    // Update status to Confirmed
                    purchaseOrder.status = "Confirmed";

                    // Call update API
                    ajax("PUT", "/PurchaseOrder/UpdatePurchaseOrder", purchaseOrder, function (updateResponse) {
                        if (updateResponse.isSuccess) {
                            showSuccessMessages(["Xác nhận đơn hàng thành công!"]);
                            $(gridId).data("kendoGrid").dataSource.filter({});
                        } else {
                            showErrorMessages(updateResponse.errorMessageList);
                        }
                    }, null, false);
                } else {
                    showErrorMessages(response.errorMessageList || ["Không thể lấy thông tin đơn hàng"]);
                }
            }, null, false);
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function viewPurchaseOrderDetail(id) {
        var response = ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, (response) => {
            showPurchaseOrderDetail(response.data);
        }, null, false);
    }

    function showPurchaseOrderDetail(data) {
        let myWindow = $("#window");
        let detailHtml = `
            <div class="purchase-order-detail">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Mã đơn hàng:</strong> ${data.code || ''}</p>
                        <p><strong>Khách hàng:</strong> ${data.customerName || ''}</p>
                        <p><strong>Trạng thái:</strong> ${data.statusName || ''}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Tổng tiền chưa thuế:</strong> ${kendo.toString(data.totalPriceNoTax || 0, "n0")} VNĐ</p>
                        <p><strong>Tổng tiền:</strong> ${kendo.toString(data.totalPrice || 0, "n0")} VNĐ</p>
                        <p><strong>Ngày tạo:</strong> ${data.createdDate ? kendo.toString(kendo.parseDate(data.createdDate), "dd/MM/yyyy HH:mm") : ''}</p>
                    </div>
                </div>
                <hr>
                <h5>Danh sách sản phẩm:</h5>
                <div id="itemsGrid"></div>
            </div>
        `;

        $("#window").html(detailHtml);

        // Initialize items grid
        $("#itemsGrid").kendoGrid({
            dataSource: {
                data: data.purchaseOrderItems || []
            },
            columns: [
                { field: "productName", title: "Sản phẩm", width: 200 },
                { field: "quantity", title: "Số lượng", width: 100, format: "{0:n2}" },
                { field: "unitName", title: "Đơn vị", width: 100 },
                { field: "price", title: "Đơn giá", width: 120, format: "{0:n0}" },
                { field: "taxRate", title: "Thuế suất (%)", width: 100, format: "{0:n1}" },
                {
                    title: "Thành tiền",
                    width: 150,
                    template: "#= kendo.toString((quantity || 0) * (price || 0), 'n0') # VNĐ"
                }
            ],
            pageable: false,
            scrollable: true,
            height: 300
        });

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "900px",
            height: "600px",
            title: "CHI TIẾT ĐƠN HÀNG PO",
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").center();
        myWindow.data("kendoWindow").open();
    }



    function generateInvoice(id) {
        window.open(`/PurchaseOrder/PreviewInvoice?purchaseOrderId=${id}`, '_blank');
    }

    function exportInvoicePdf(id) {
        window.open(`/PurchaseOrder/ExportInvoicePdf?purchaseOrderId=${id}`, '_blank');
    }

    function exportInvoiceExcel(id) {
        window.open(`/PurchaseOrder/ExportInvoiceExcel?purchaseOrderId=${id}`, '_blank');
    }

    function exportInvoiceWord(id) {
        window.open(`/PurchaseOrder/ExportInvoiceWord?purchaseOrderId=${id}`, '_blank');
    }

    function toggleInvoiceDropdown(button, id) {
        // Close all existing dropdowns
        $('.invoice-dropdown-overlay').remove();

        // Create dropdown menu HTML
        var dropdownHtml = `
            <div class="invoice-dropdown-overlay" style="
                position: fixed;
                z-index: 99999;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                min-width: 180px;
                padding: 8px 0;
                font-size: 14px;
            ">
                <a href="javascript:void(0)" onclick="generateInvoice(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-eye" style="margin-right: 8px; width: 16px;"></i>Xem trước
                </a>
                <a href="javascript:void(0)" onclick="exportInvoicePdf(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-pdf" style="margin-right: 8px; width: 16px; color: #dc3545;"></i>Xuất PDF
                </a>
                <a href="javascript:void(0)" onclick="exportInvoiceExcel(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-excel" style="margin-right: 8px; width: 16px; color: #198754;"></i>Xuất Excel
                </a>
                <a href="javascript:void(0)" onclick="exportInvoiceWord(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-word" style="margin-right: 8px; width: 16px; color: #0d6efd;"></i>Xuất Word
                </a>
            </div>
        `;

        // Get button position
        var buttonOffset = $(button).offset();
        var buttonHeight = $(button).outerHeight();
        var buttonWidth = $(button).outerWidth();

        // Calculate position
        var top = buttonOffset.top + buttonHeight + 4;
        var left = buttonOffset.left;

        // Adjust if menu would go off screen
        var windowWidth = $(window).width();
        var windowHeight = $(window).height();

        // Create temporary element to measure dropdown size
        var $tempDropdown = $(dropdownHtml).css({
            'position': 'fixed',
            'top': '-9999px',
            'left': '-9999px',
            'visibility': 'hidden'
        });
        $('body').append($tempDropdown);

        var menuWidth = $tempDropdown.outerWidth();
        var menuHeight = $tempDropdown.outerHeight();
        $tempDropdown.remove();

        // Adjust position if needed
        if (left + menuWidth > windowWidth - 20) {
            left = buttonOffset.left + buttonWidth - menuWidth;
        }

        if (top + menuHeight > windowHeight - 20) {
            top = buttonOffset.top - menuHeight - 4;
        }

        // Create and position the dropdown
        var $dropdown = $(dropdownHtml).css({
            'top': top + 'px',
            'left': left + 'px'
        });

        // Add hover effects
        $dropdown.find('a').hover(
            function () {
                $(this).css('background-color', '#f8f9fa');
            },
            function () {
                $(this).css('background-color', 'transparent');
            }
        );

        // Append to body
        $('body').append($dropdown);

        // Add fade in effect
        $dropdown.hide().fadeIn(150);
    }

    // Close dropdown when clicking outside
    $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length && !$(e.target).closest('.invoice-dropdown-overlay').length && !$(e.target).closest('.btn-invoice').length) {
            $('.invoice-dropdown-overlay').remove();
        }
    });

    function renderPurchaseOrderForm(data = null) {
        var isEdit = data !== null && data !== undefined;
        var windowTitle = isEdit ? "CHỈNH SỬA ĐƠN HÀNG PO" : "THÊM MỚI ĐƠN HÀNG PO";

        let formHtml = `
            <div class="">
                <!-- Header Info Section -->
                <div class="po-header-section">
                    <div class="header-grid-3col">
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Khách hàng (*)</label>
                                <select id="po-customerId" class="form-control"></select>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Trạng thái</label>
                                <select id="po-status" class="form-control"></select>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Mã đơn hàng</label>
                                <input type="text" id="po-code" class="form-control" readonly placeholder="Tự động tạo"/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Tổng tiền chưa thuế</label>
                                <input type="text" id="po-totalPriceNoTax" class="form-control" readonly/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Tổng tiền</label>
                                <input type="text" id="po-totalPrice" class="form-control" readonly/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Item Input Section -->
                <div class="po-item-section">
                    <h5>Thêm sản phẩm vào đơn hàng</h5>
                    <div class="item-input-grid">
                        <!-- Row 1: 4 columns -->
                        <div class="grid-row-4col">
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Sản phẩm (*)</label>
                                    <select id="item-productId" class="form-control"></select>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Số lượng (*)</label>
                                    <input type="number" id="item-quantity" class="form-control" step="0.01" min="0"/>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Đơn vị</label>
                                    <input type="text" id="item-unitName" class="form-control" readonly/>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Đơn giá</label>
                                    <input type="number" id="item-price" class="form-control" step="0.01" min="0"/>
                                </div>
                            </div>
                        </div>
                        <!-- Row 2: 4 columns -->
                        <div class="grid-row-4col">
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Hao hụt (%)</label>
                                    <input type="number" id="item-lossRate" class="form-control" step="0.1" min="0" max="100"/>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Thuế (%)</label>
                                    <input type="number" id="item-taxRate" class="form-control" step="0.1" min="0" max="100"/>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Phí gia công</label>
                                    <input type="number" id="item-processingFee" class="form-control" step="0.01" min="0"/>
                                </div>
                            </div>
                            <div class="grid-col-4-1">
                                <div class="form-group">
                                    <label>Lợi nhuận (%)</label>
                                    <input type="number" id="item-profitMargin" class="form-control" step="0.1" min="0"/>
                                </div>
                            </div>
                        </div>
                        <!-- Row 3: 4 columns -->
                        <div class="grid-row-4col">
                            <div class="grid-col-4-2">
                                <div class="form-group">
                                    <label>Loại gia công</label>
                                    <select id="item-processingTypeId" class="form-control"></select>
                                </div>
                            </div>
                            <div class="grid-col-4-2">
                                <div class="form-group">
                                    <label>Ghi chú</label>
                                    <input type="text" id="item-note" class="form-control"/>
                                </div>
                            </div>
                        </div>
                        <!-- Row 4: Button row -->
                        <div class="grid-row-button">
                            <div class="form-group">
                                <button type="button" id="addItemBtn" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Thêm sản phẩm
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Items List Section -->
                <div class="po-items-section">
                    <h5>Danh sách sản phẩm</h5>
                    <div id="itemsGrid"></div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" id="savePOBtn" class="btn btn-success">
                        <i class="fas fa-save"></i> ${isEdit ? 'Cập nhật' : 'Lưu'}
                    </button>
                    <button type="button" id="cancelPOBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Hủy
                    </button>
                </div>
            </div>
        `;

        let myWindow = $("#window");
        myWindow.html(formHtml);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: windowTitle,
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").center();
        myWindow.data("kendoWindow").open();

        // Initialize form after window opens
        setTimeout(function () {
            initializePurchaseOrderForm(data);
        }, 100);
    }

    function initializePurchaseOrderForm(data) {
        var isEdit = data !== null && data !== undefined;
        var poItems = [];

        // Initialize Customer Dropdown
        $("#po-customerId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn khách hàng --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn khách hàng --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Customer" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Initialize Status Dropdown
        $("#po-status").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "dataRaw",
            optionLabel: "-- Chọn trạng thái --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn trạng thái --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "POStatus" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.dataRaw !== undefined;
                            });
                        }
                        return [];
                    }
                }
            },
            dataBound: function () {
                // Always disable status dropdown - status can only be changed via action buttons
                this.enable(false);

                // Set default status to "New" for new orders
                if (!isEdit) {
                    // Find the item with "New" status (by enum value)
                    var dataSource = this.dataSource;
                    var newStatusItem = null;
                    for (var i = 0; i < dataSource.data().length; i++) {
                        var item = dataSource.data()[i];
                        // Compare by enum value - look for "New"
                        if (item.dataRaw === "New") {
                            newStatusItem = item;
                            break;
                        }
                    }

                    if (newStatusItem) {
                        this.value(newStatusItem.dataRaw);
                    }
                }
            }
        });

        // Initialize Product Dropdown with Dynamic Search
        $("#item-productId").kendoComboBox({
            dataTextField: "text",
            dataValueField: "value",
            placeholder: "Tìm kiếm sản phẩm...",
            clearButton: false,
            suggest: true,
            filter: "contains", // Enable filtering to trigger server-side search
            minLength: 1,
            highlightFirst: true,
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            change: function (e) {
                var selectedProduct = this.dataItem();
                if (selectedProduct && selectedProduct.value) {
                    // Load product details
                    loadProductDetails(selectedProduct.value);
                } else {
                    // Clear product details if no valid selection
                    $("#item-unitName").val('');
                    $("#item-price").val('');
                    $("#item-taxRate").val('');
                }
            },
            select: function (e) {
                var selectedProduct = this.dataItem(e.item.index());
                if (selectedProduct && selectedProduct.value) {
                    // Load product details
                    loadProductDetails(selectedProduct.value);
                }
            },

            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: {
                            type: "Product",
                            searchString: "" // Default empty search - loads first products
                        }
                    },
                    parameterMap: function (data, type) {
                        if (type === "read") {
                            return {
                                type: "Product",
                                searchString: data.filter && data.filter.filters && data.filter.filters.length > 0
                                    ? data.filter.filters[0].value
                                    : ""
                            };
                        }
                        return data;
                    }
                },
                serverFiltering: true,
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            },
            height: 300,
            animation: {
                open: {
                    effects: "fadeIn",
                    duration: 200
                }
            }
        });

        // Initialize Processing Type Dropdown
        $("#item-processingTypeId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn loại gia công --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn loại gia công --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "ProcessingType" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Initialize Items Grid
        $("#itemsGrid").kendoGrid({
            dataSource: {
                data: poItems,
                schema: {
                    model: {
                        id: "tempId",
                        fields: {
                            tempId: { type: "string" },
                            productId: { type: "number" },
                            productName: { type: "string" },
                            quantity: { type: "number" },
                            unitName: { type: "string" },
                            price: { type: "number" },
                            lossRate: { type: "number" },
                            taxRate: { type: "number" },
                            processingFee: { type: "number" },
                            profitMargin: { type: "number" },
                            processingTypeId: { type: "number" },
                            processingTypeName: { type: "string" },
                            note: { type: "string" },
                            totalAmount: { type: "number" }
                        }
                    }
                }
            },
            columns: [
                { field: "productName", title: "Sản phẩm", width: "150px" },
                { field: "quantity", title: "Số lượng", width: "80px", template: "#= kendo.toString(quantity, 'n2') #" },
                { field: "unitName", title: "Đơn vị", width: "80px" },
                { field: "price", title: "Đơn giá", width: "100px", template: "#= kendo.toString(price, 'n0') # VNĐ" },
                { field: "lossRate", title: "Hao hụt (%)", width: "80px", template: "#= lossRate ? kendo.toString(lossRate, 'n1') + '%' : '' #" },
                { field: "taxRate", title: "Thuế (%)", width: "80px", template: "#= taxRate ? kendo.toString(taxRate, 'n1') + '%' : '' #" },
                { field: "processingFee", title: "Phí gia công", width: "100px", template: "#= processingFee ? kendo.toString(processingFee, 'n0') + ' VNĐ' : '' #" },
                { field: "profitMargin", title: "Lợi nhuận (%)", width: "90px", template: "#= profitMargin ? kendo.toString(profitMargin, 'n1') + '%' : '' #" },
                { field: "processingTypeName", title: "Loại gia công", width: "120px" },
                { field: "note", title: "Ghi chú", width: "150px" },
                { field: "totalAmount", title: "Thành tiền", width: "120px", template: "#= kendo.toString(totalAmount, 'n0') # VNĐ" },
                {
                    title: "Thao tác",
                    width: "80px",
                    template: '<button class="btn btn-sm btn-danger" onclick="removeItem(\'#= tempId #\')"><i class="fas fa-trash"></i></button>'
                }
            ],
            pageable: false,
            scrollable: true,
            height: 300
        });

        // Event Handlers
        $("#addItemBtn").click(function () {
            addItemToGrid();
        });

        $("#savePOBtn").click(function () {
            savePurchaseOrder();
        });

        $("#cancelPOBtn").click(function () {
            $("#window").data("kendoWindow").close();
        });

        // Load data if editing
        if (isEdit && data) {
            // Delay to ensure dropdowns are initialized
            setTimeout(function () {
                loadPurchaseOrderData(data);
            }, 200);
        }
    }

    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString" placeholder="Nhập mã đơn hàng..."/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="customerId">Khách hàng:</label>
                                    <select id="customerId" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="status">Trạng thái:</label>
                                    <select id="status" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateFrom">Từ ngày:</label>
                                    <input type="text" class="w-100" id="dateFrom"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateTo">Đến ngày:</label>
                                    <input type="text" class="w-100" id="dateTo"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm đơn hàng mới" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='32'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm mới</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/PurchaseOrder/GetPurchaseOrderList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }
                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },
                            totalPrice: { type: "number" },
                            totalPriceNoTax: { type: "number" }
                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            detailInit: detailInit,
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: 80
                },
                {
                    field: "code",
                    title: "Mã đơn hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 150,
                },
                {
                    field: "customerName",
                    title: "Khách hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                    width: 200,
                },
                {
                    field: "statusName",
                    title: "Trạng thái",
                    width: 130,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: function (dataItem) {
                        // Add null check for dataItem
                        if (!dataItem) {
                            return '<span class="badge badge-secondary">N/A</span>';
                        }

                        let statusClass = "";
                        // Status có thể được lưu dưới dạng enum name hoặc enum value
                        switch (dataItem.status) {
                            case "New":
                            case "0":
                                statusClass = "badge-secondary"; break; // Mới
                            case "Confirmed":
                            case "1":
                                statusClass = "badge-success"; break;   // Đã xác nhận
                            case "Executed":
                            case "2":
                                statusClass = "badge-info"; break;      // Đã tạo đơn tổng hợp
                            case "Cancel":
                            case "3":
                                statusClass = "badge-danger"; break;    // Đã hủy
                            default: statusClass = "badge-secondary";
                        }
                        return `<span class="badge ${statusClass}">${dataItem.statusName || 'N/A'}</span>`;
                    }
                },
                {
                    field: "totalPriceNoTax",
                    title: "Tổng tiền chưa thuế",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    template: "<span class='currency'>#: kendo.toString(totalPriceNoTax, 'n0') # VNĐ</span>",
                },
                {
                    field: "totalPrice",
                    title: "Tổng tiền",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    template: "<span class='currency'>#: kendo.toString(totalPrice, 'n0') # VNĐ</span>",
                },
                {
                    field: "createdDate",
                    title: "Ngày tạo",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(createdDate), "dd/MM/yyyy HH:mm")#',
                },
                {
                    field: "",
                    title: "Thao tác",
                    width: 200,
                    attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        var confirmButton = '';
                        var cancelButton = '';

                        // Show confirm button only for orders with status "New" (0)
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) {
                            confirmButton = '<button onclick="confirmPurchaseOrder(' + dataItem.id + ')" title="Xác nhận đơn hàng" class="btn-action btn-confirm _permission_" data-enum="33">' +
                                '<i class="fas fa-check"></i>' +
                                '</button>';
                        }

                        // Show cancel button only for orders with New (0) and Confirmed (1) statuses
                        if ((dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) ||
                            (dataItem.status === "Confirmed" || dataItem.status === "1" || dataItem.status === 1)) {
                            cancelButton = '<button onclick="cancelPurchaseOrder(' + dataItem.id + ')" title="Hủy đơn hàng" class="btn-action btn-cancel _permission_" data-enum="33">' +
                                '<i class="fas fa-ban"></i>' +
                                '</button>';
                        }

                        // Show delete button only for orders with New (0) status
                        var deleteButton = '';
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) {
                            deleteButton = '<button onclick="deletePurchaseOrder(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="34">' +
                                '<i class="fas fa-trash"></i>' +
                                '</button>';
                        }

                        // Show edit button only for orders with New (0) status
                        var editButton = '';
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) {
                            editButton = '<button onclick="editPurchaseOrder(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="33">' +
                                '<i class="fas fa-edit"></i>' +
                                '</button>';
                        }

                        // Show invoice button only for orders that are not cancelled
                        var invoiceButton = '';
                        if (dataItem.status !== "Cancel" && dataItem.status !== "3" && dataItem.status !== 3) {
                            invoiceButton = '<button class="btn-action btn-invoice" type="button" onclick="toggleInvoiceDropdown(this, ' + dataItem.id + ')" title="Xuất hóa đơn">' +
                                '<i class="fas fa-file-invoice"></i>' +
                                '</button>';
                        }

                        return '<div class="action-buttons">' +
                            editButton +
                            deleteButton +
                            confirmButton +
                            cancelButton +
                            invoiceButton +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });
    }

    function detailInit(e) {
        $("<div/>").appendTo(e.detailCell).kendoGrid({
            dataSource: {
                data: e.data.purchaseOrderItems || [],
                schema: {
                    model: {
                        fields: {
                            quantity: { type: "number" },
                            price: { type: "number" },
                            taxRate: { type: "number" },
                            lossRate: { type: "number" },
                            processingFee: { type: "number" },
                            profitMargin: { type: "number" }
                        }
                    }
                }
            },
            scrollable: false,
            sortable: true,
            pageable: false,
            columns: [
                { field: "productName", title: "Sản phẩm", width: "200px" },
                { field: "quantity", title: "Số lượng", width: "100px", template: "#= '<span class=\"number\">' + kendo.toString(quantity, 'n2') + '</span>' #" },
                { field: "unitName", title: "Đơn vị", width: "100px" },
                { field: "price", title: "Đơn giá", width: "120px", template: "#= price ? '<span class=\"currency\">' + kendo.toString(price, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "taxRate", title: "Thuế suất (%)", width: "100px", template: "#= taxRate ? '<span class=\"number\">' + kendo.toString(taxRate, 'n1') + '%</span>' : '' #" },
                { field: "lossRate", title: "Tỷ lệ hao hụt (%)", width: "120px", template: "#= lossRate ? '<span class=\"number\">' + kendo.toString(lossRate, 'n1') + '%</span>' : '' #" },
                { field: "processingFee", title: "Phí gia công", width: "120px", template: "#= processingFee ? '<span class=\"currency\">' + kendo.toString(processingFee, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "profitMargin", title: "Tỷ lệ lợi nhuận (%)", width: "130px", template: "#= profitMargin ? '<span class=\"number\">' + kendo.toString(profitMargin, 'n1') + '%</span>' : '' #" },
                {
                    title: "Thành tiền",
                    width: "150px",
                    template: "#= '<span class=\"currency\">' + kendo.toString((quantity || 0) * (price || 0), 'n0') + ' VNĐ</span>' #"
                },
                { field: "note", title: "Ghi chú", width: "200px" }
            ]
        });
    }

    function InitKendoToolBar() {
        // Initialize Kendo components
        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });

        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });

        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"
            },
            placeholder: "Nhập mã đơn hàng..."
        });

        // Initialize Date Pickers
        $("#dateFrom").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN"
        });

        $("#dateTo").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN"
        });

        // Initialize customer dropdown with Kendo DropDownList
        $("#customerId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn khách hàng --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn khách hàng --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Customer" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                },

            }
        });
        // Initialize status dropdown
        $("#status").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "dataRaw",
            optionLabel: "-- Chọn trạng thái --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn trạng thái --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "POStatus" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.dataRaw !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Create button click handler
        $("#create").on('click', function () {
            renderPurchaseOrderForm();
        });

        // Set default date range to last 1 month
        var today = new Date();
        var oneMonthAgo = new Date();
        oneMonthAgo.setMonth(today.getMonth() - 1);

        // Set default values for date pickers
        $("#dateFrom").data("kendoDatePicker").value(oneMonthAgo);
        $("#dateTo").data("kendoDatePicker").value(today);
    }

    // Helper Functions for Purchase Order Form
    function loadProductDetails(productId) {
        ajax("GET", "/Product/GetProductById/" + productId, {}, function (response) {
            if (response.isSuccess && response.data) {
                var product = response.data;
                $("#item-unitName").val(product.unitName || '');
                $("#item-price").val(product.price || 0);
                $("#item-taxRate").val(product.taxRate || 0);
            } else {
                // Show error notification
                showErrorMessages(response.errorMessageList || ["Không thể tải thông tin sản phẩm"]);
            }
        }, function (error) {
            // Handle AJAX error
            showErrorMessages(["Lỗi khi tải thông tin sản phẩm"]);
        }, false);
    }

    function addItemToGrid() {
        var productDropdown = $("#item-productId").data("kendoComboBox");
        var processingTypeDropdown = $("#item-processingTypeId").data("kendoDropDownList");

        var productId = productDropdown ? productDropdown.value() : "";
        var productName = productDropdown ? productDropdown.text() : "";
        var quantity = parseFloat($("#item-quantity").val()) || 0;
        var unitName = $("#item-unitName").val();
        var price = parseFloat($("#item-price").val()) || 0;
        var lossRate = parseFloat($("#item-lossRate").val()) || 0;
        var taxRate = parseFloat($("#item-taxRate").val()) || 0;
        var processingFee = parseFloat($("#item-processingFee").val()) || 0;
        var profitMargin = parseFloat($("#item-profitMargin").val()) || 0;
        var processingTypeId = processingTypeDropdown.value();
        var processingTypeName = processingTypeDropdown.text();
        var note = $("#item-note").val();

        // Validation
        if (!productId || productId === "" || productName === "-- Chọn sản phẩm --") {
            showErrorMessages(["Vui lòng chọn sản phẩm"]);
            return;
        }
        if (quantity <= 0) {
            showErrorMessages(["Vui lòng nhập số lượng hợp lệ"]);
            return;
        }

        // Calculate total amount
        var baseAmount = quantity * price;
        var lossAmount = baseAmount * (lossRate / 100);
        var totalBeforeTax = baseAmount + lossAmount + processingFee;
        var profitAmount = totalBeforeTax * (profitMargin / 100);
        var totalAfterProfit = totalBeforeTax + profitAmount;
        var taxAmount = totalAfterProfit * (taxRate / 100);
        var totalAmount = totalAfterProfit + taxAmount;

        var newItem = {
            tempId: generateTempId(),
            productId: parseInt(productId),
            productName: productName,
            quantity: quantity,
            unitName: unitName,
            price: price,
            lossRate: lossRate,
            taxRate: taxRate,
            processingFee: processingFee,
            profitMargin: profitMargin,
            processingTypeId: processingTypeId ? parseInt(processingTypeId) : null,
            processingTypeName: processingTypeName === "-- Chọn loại gia công --" ? "" : processingTypeName,
            note: note,
            totalAmount: totalAmount
        };

        var grid = $("#itemsGrid").data("kendoGrid");
        grid.dataSource.add(newItem);

        // Clear form
        clearItemForm();

        // Update totals
        updatePurchaseOrderTotals();
    }

    function removeItem(tempId) {
        var grid = $("#itemsGrid").data("kendoGrid");
        var dataItem = grid.dataSource.get(tempId);
        if (dataItem) {
            grid.dataSource.remove(dataItem);
            updatePurchaseOrderTotals();
        }
    }

    function clearItemForm() {
        var productComboBox = $("#item-productId").data("kendoComboBox");
        if (productComboBox) {
            productComboBox.value("");
        }
        $("#item-quantity").val("");
        $("#item-unitName").val("");
        $("#item-price").val("");
        $("#item-lossRate").val("");
        $("#item-taxRate").val("");
        $("#item-processingFee").val("");
        $("#item-profitMargin").val("");
        var processingTypeDropdown = $("#item-processingTypeId").data("kendoDropDownList");
        if (processingTypeDropdown) {
            processingTypeDropdown.value("");
        }
        $("#item-note").val("");
    }

    function updatePurchaseOrderTotals() {
        var grid = $("#itemsGrid").data("kendoGrid");
        var items = grid.dataSource.data();

        var totalPriceNoTax = 0;
        var totalPrice = 0;

        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            var baseAmount = item.quantity * item.price;
            var lossAmount = baseAmount * (item.lossRate / 100);
            var totalBeforeTax = baseAmount + lossAmount + item.processingFee;
            var profitAmount = totalBeforeTax * (item.profitMargin / 100);
            var totalAfterProfit = totalBeforeTax + profitAmount;
            var taxAmount = totalAfterProfit * (item.taxRate / 100);

            totalPriceNoTax += totalAfterProfit;
            totalPrice += totalAfterProfit + taxAmount;
        }

        $("#po-totalPriceNoTax").val(kendo.toString(totalPriceNoTax, "n0") + " VNĐ");
        $("#po-totalPrice").val(kendo.toString(totalPrice, "n0") + " VNĐ");
    }

    function generateTempId() {
        return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    function loadPurchaseOrderData(data) {
        // Load header data
        try {
            if (data.customer_ID) {
                var customerDropdown = $("#po-customerId").data("kendoDropDownList");
                if (customerDropdown) {
                    customerDropdown.value(data.customer_ID);
                }
            }

            if (data.status !== undefined) {
                var statusDropdown = $("#po-status").data("kendoDropDownList");
                if (statusDropdown) {
                    statusDropdown.value(data.status);
                }
            }

            $("#po-code").val(data.code || '');

            // Load items
            if (data.purchaseOrderItems && data.purchaseOrderItems.length > 0) {
                var grid = $("#itemsGrid").data("kendoGrid");
                if (grid) {
                    // Clear existing data first
                    grid.dataSource.data([]);

                    for (var i = 0; i < data.purchaseOrderItems.length; i++) {
                        var item = data.purchaseOrderItems[i];
                        // Map server field names to client field names
                        var gridItem = {
                            tempId: generateTempId(),
                            productId: item.product_ID || item.productId,
                            productName: item.productName,
                            quantity: item.quantity,
                            unitName: item.unitName,
                            price: item.price,
                            lossRate: item.lossRate,
                            taxRate: item.taxRate,
                            processingFee: item.processingFee,
                            profitMargin: item.profitMargin,
                            processingTypeId: item.processingType_ID || item.processingTypeId,
                            processingTypeName: item.processingTypeName,
                            note: item.note,
                            totalAmount: (item.quantity || 0) * (item.price || 0)
                        };
                        grid.dataSource.add(gridItem);
                    }
                    updatePurchaseOrderTotals();
                }
            }

            // Load totals
            if (data.totalPriceNoTax !== undefined) {
                $("#po-totalPriceNoTax").val(kendo.toString(data.totalPriceNoTax, "n0") + " VNĐ");
            }
            if (data.totalPrice !== undefined) {
                $("#po-totalPrice").val(kendo.toString(data.totalPrice, "n0") + " VNĐ");
            }

        } catch (error) {
            // Error loading PO data
        }
    }

    function savePurchaseOrder() {
        var customerDropdown = $("#po-customerId").data("kendoDropDownList");
        var statusDropdown = $("#po-status").data("kendoDropDownList");
        var grid = $("#itemsGrid").data("kendoGrid");

        var customerId = customerDropdown.value();
        var status = statusDropdown.value();
        var items = grid.dataSource.data();

        // Validation
        if (!customerId || customerId === "") {
            showErrorMessages(["Vui lòng chọn khách hàng"]);
            return;
        }

        if (items.length === 0) {
            showErrorMessages(["Vui lòng thêm ít nhất một sản phẩm"]);
            return;
        }

        // Prepare data
        var purchaseOrderData = {
            customer_ID: parseInt(customerId),
            status: status || 0,
            purchaseOrderItems: items.map(function (item) {
                return {
                    productId: item.productId,
                    quantity: item.quantity,
                    price: item.price,
                    lossRate: item.lossRate,
                    taxRate: item.taxRate,
                    processingFee: item.processingFee,
                    profitMargin: item.profitMargin,
                    processingTypeId: item.processingTypeId,
                    note: item.note
                };
            })
        };

        // Save
        var url = "/PurchaseOrder/CreatePurchaseOrder";
        var method = "POST";

        ajax(method, url, purchaseOrderData, function (response) {
            if (response.isSuccess) {
                showSuccessMessages(["Lưu đơn hàng thành công!"]);
                $("#window").data("kendoWindow").close();
                $(gridId).data("kendoGrid").dataSource.read();
            }
        }, null, false);
    }
</script>

<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

        // Auto load data with default filter (last 1 month)
        setTimeout(function () {

            $(window).trigger("resize");
        }, 500);
    });
</script>

<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Page specific overrides */
    .purchase-order-toolbar {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Custom dropdown overlay styling */
    .invoice-dropdown-overlay {
        font-family: inherit;
        line-height: 1.5;
    }

    .invoice-dropdown-overlay a {
        transition: background-color 0.15s ease-in-out;
    }

    .invoice-dropdown-overlay a:hover {
        background-color: #f8f9fa !important;
    }

    /* Confirm button styling */
    .btn-action.btn-confirm {
        background-color: #28a745;
        color: white;
        border: 1px solid #28a745;
    }

    .btn-action.btn-confirm:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    /* Force 4-column layout for item input */
    .item-input-grid .grid-row-4col {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 15px !important;
        margin-bottom: 15px;
        align-items: end;
    }

    .grid-row-4col {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 15px !important;
        margin-bottom: 15px;
        align-items: end;
    }

    .grid-col-4-1 {
        grid-column: span 1 !important;
    }

    .grid-col-4-2 {
        grid-column: span 2 !important;
    }

    .grid-row-button {
        display: flex !important;
        justify-content: flex-end !important;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .grid-row-button .form-group {
        margin-bottom: 0;
    }

    .grid-row-button .btn {
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
    }

    /* 3-Column Grid Layout for Header */
    .header-grid-3col {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 20px !important;
        margin-bottom: 0;
        align-items: end;
    }

    .header-col-3-1 {
        grid-column: span 1 !important;
    }

    .header-col-3-2 {
        grid-column: span 2 !important;
    }

    .header-col-3-3 {
        grid-column: span 3 !important;
    }

    /* Responsive for header grid */
    @@media (max-width: 992px) {
        .header-grid-3col {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    @@media (max-width: 768px) {
        .header-grid-3col {
            grid-template-columns: 1fr !important;
            gap: 15px !important;
        }
    }

    /* Disabled status dropdown styling */
    .k-dropdown.k-disabled {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        opacity: 0.8;
        cursor: not-allowed;
    }

    .k-dropdown.k-disabled .k-dropdown-wrap {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed;
    }

    .k-dropdown.k-disabled .k-select {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        cursor: not-allowed;
    }

    /* Add a subtle indicator that status is read-only */
    .k-dropdown.k-disabled::after {
        content: "🔒";
        position: absolute;
        right: 25px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        opacity: 0.6;
        pointer-events: none;
    }
</style>
